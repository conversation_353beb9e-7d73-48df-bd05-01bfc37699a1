<projectSpec>
  <project
        name="8_1_4"
        device="TMS320F280049M"
        cgtVersion="16.9.1.LTS"
        launchWizard="False"
        linkerCommandFile=""
        >
    <configuration name="1_RamDebug" compilerBuildOptions="--opt_level=off -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0  --define=CPU1  --diag_warning=225  --diag_suppress=10063 --display_error_number" linkerBuildOptions="--stack_size=0x30A -heap=0 --entry_point code_start" />
    <configuration name="2_RamRelease" compilerBuildOptions="--opt_level=3 -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0  --define=CPU1  --diag_warning=225  --diag_suppress=10063 --display_error_number" linkerBuildOptions="--stack_size=0x30A -heap=0 --entry_point code_start" />
    <configuration name="3_FlashRelease" compilerBuildOptions="--opt_level=3 -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0  --define=CPU1 --define=_FLASH --diag_warning=225  --diag_suppress=10063 --display_error_number" linkerBuildOptions="--stack_size=0x30A -heap=0 --entry_point code_start" />
    <configuration name="4_FlashRamRelease" compilerBuildOptions="--opt_level=3 -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0  --define=CPU1 --define=_FLASH --diag_warning=225  --diag_suppress=10063 --display_error_number" linkerBuildOptions="--stack_size=0x30A -heap=0 --entry_point code_start" />

    <file action="link" path="8_1_4.c" targetDirectory="" />



    <file action="link" path="PsBiosRamF004xFloat.lib" targetDirectory="" applicableConfigurations="1_RamDebug,2_RamRelease" />
    <file action="link" path="PsBiosRomF004xFloat.lib" targetDirectory="" applicableConfigurations="3_FlashRelease,4_FlashRamRelease" />

    <file action="copy" path="f28004x_headers_nonbios.cmd" targetDirectory="" />
    <file action="copy" path="F280049_RAM_lnk.cmd" targetDirectory="" applicableConfigurations="1_RamDebug,2_RamRelease" />
    <file action="copy" path="F280049_FLASH_lnk.cmd" targetDirectory="" applicableConfigurations="3_FlashRelease" />
    <file action="copy" path="F280049_FLASH_RAM_lnk.cmd" targetDirectory="" applicableConfigurations="4_FlashRamRelease" />
  </project>
</projectSpec>
